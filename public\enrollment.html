<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Checkout Interface</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#f97316', 
            secondary: '#2c4da5f3',  
          },
          fontFamily: {
            'poppins': ['Poppins', 'sans-serif'],  
          },
        },
      },
    }
  </script>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

    body, html {
      margin: 0;
      padding: 0;
      font-family: 'Poppins', sans-serif;
    }
    

    .glass-container {
      background: rgba(255, 255, 255, 0.705);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      border: 1px solid rgba(255, 255, 255, 0.18);
      box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
      width: 100%;
      max-width: 1024px;
      margin: 0 auto;
      padding: 2rem;
    }

    h1, h2 {
      font-weight: 600;
      color: #2c4da5f3;  /* Use the secondary blue color */
      text-align: center;
    }

    h1 {
      font-size: 1.5rem;
      margin-bottom: 1rem;
    }

    h2 {
      font-size: 1.5rem;
      margin-bottom: 0.5rem;
    }

    p {
      font-size: 0.9rem;  /* Match with text size from learningpathui */
      color: #4a5568;  /* Text gray color */
      text-align: center;
      margin-bottom: 1.5rem;
    }

    input[type="text"] {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 10px;
      background: rgba(255, 255, 255, 0.5);
      backdrop-filter: blur(5px);
      font-size: 1rem;
      color: #2c4da5f3;  
      margin-bottom: 1.5rem;
      transition: all 0.3s ease;
    }

    input[type="text"]::placeholder {
      color: #6b7280;
    }

    input[type="text"]:focus {
      outline: none;
      background: rgba(255, 255, 255, 0.8);
    }

    .pathway-section {
      margin-bottom: 2rem;
    }

    .pathway-header {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 1rem;
    }

    .pathway-icon {
      margin-left: auto;
      padding: 0.75rem;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #e0f2fe;
      color: #0284c7;
    }

    .pathway-title {
      font-size: 1.5rem;
      color: #2c4da5f3;
      text-align: center;
    }

    .pathway-description {
      text-align: center;
      font-size: 0.9rem;  /* Adjusted to match the text size in learningpathui */
      color: #4b5563;
      margin-bottom: 1.5rem;
    }

    .learners-list {
      max-height: 300px;
      overflow-y: auto;
      border-top: 1px solid rgba(255, 255, 255, 0.3);
      padding-top: 1rem;
    }

    .learner-card {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-radius: 8px;
  transition: background-color 0.3s ease;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.learner-card:hover {
  background-color: #f3f4f6; /* Subtle hover effect */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.learner-name {
  font-size: 0.875rem; /* Small font for minimalism */
  font-weight: 500;
  color: #2c4da5f3; /* Theme's primary color */
}

.learner-status {
  font-size: 0.75rem;
}

.enroll-checkbox {
  width: 18px;
  height: 18px;
  margin-left: auto; /* Align checkbox to the right */
}


.toggle-enrolled-visibility {
  transition: color 300ms ease-in-out;
}

.toggle-enrolled-visibility:hover {
  color: #1f2937; /* Tailwind's gray-800 */
}
    #proceedToPayment {
      background-color: #2c4da5f3;
      color: white;
      padding: 0.75rem 2rem;
      border-radius: 10px;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.3s ease;
      margin-top: 1rem;
    }

    #proceedToPayment:disabled {
      background-color: #9ca3af;
      cursor: not-allowed;
    }

    .total-cost-container {
      background: rgba(255, 255, 255, 0.7);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      padding: 1.5rem;
      margin-top: 2rem;
      box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.1);
      text-align: center;
      border: 1px solid rgba(255, 255, 255, 0.18);
    }

    .total-cost {
      font-size: 2.5rem;
      font-weight: 700;
      color: #f97316;
      margin-bottom: 1rem;
    }

    @media screen and (max-width: 768px) {
      .learner-card {
        flex-direction: column;
        align-items: flex-start;
      }

      .learner-details {
        flex-direction: column;
        align-items: flex-start;
      }

      .enroll-checkbox {
        margin-top: 0.5rem;
      }
    }
      .btn-back {
      position: fixed;
      right: 25px;
      background-color: rgba(255, 255, 255, 0.2);
      color: #0f6ef1;
      backdrop-filter: blur(2px);
      padding: 8px 16px;
      font-size: 16px;
      z-index: 1;
      }

      .btn-back:hover {
          background-color: rgba(255, 255, 255, 0.3);
      }
      .learners-grid {
      max-height: calc(4 * (80px + 1rem));  /* Adjust based on your card height */
      overflow-y: auto;
    }
    .learners-grid::-webkit-scrollbar {
      width: 6px;
    }
    .learners-grid::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
    .learners-grid::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 3px;
    }
    .learners-grid::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
    .notification {
  animation: fadeInOut 5s ease-in-out;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0; }
  10%, 90% { opacity: 1; }
}
.custom-processing-color {
    color:  #FFA500 !important;
}

/* Overlay Styles */
.overlay {
    position: fixed;
    inset: 0;
    background-color: rgba(75, 85, 99, 0.5);
    overflow-y: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
}

.overlay-content {
    background-color: #ffffff;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    max-width: 768px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
}

.overlay-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #1e40af;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 8px;
}

/* Recommendations Controls */
.recommendations-container {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
    flex-wrap: wrap;
}

.recommendations-divider {
    height: 24px;
    width: 1px;
    background-color: #e5e7eb;
}

.recommendations-toggle-container {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0 8px;
}

/* Toggle Switch Styles */
.toggle-switch {
    position: relative;
    display: inline-flex;
    width: 44px;
    height: 24px;
    margin-right: 8px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    inset: 0;
    background-color: #e5e7eb;
    border-radius: 24px;
    transition: 0.4s;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    border-radius: 50%;
    transition: 0.4s;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggle-switch input:checked + .toggle-slider {
    background-color: #2563eb;
}

.toggle-switch input:checked + .toggle-slider:before {
    transform: translateX(20px);
}

.toggle-switch input:focus + .toggle-slider {
    box-shadow: 0 0 1px #2563eb;
}

.toggle-label {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    user-select: none;
}

/* Button Styles */
.button-container,
.action-button-container {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.toggle-button,
.action-button {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    font-weight: 500;
    border: none;
    transition: all 0.2s ease;
}

.toggle-button:hover,
.action-button:hover {
    transform: translateY(-1px);
}

.toggle-button:active,
.action-button:active {
    transform: translateY(0);
}

.select-all-button {
    background-color: #4CAF50;
    color: #ffffff;
}

.select-all-button:hover {
    background-color: #45a049;
}

.select-recommended-button {
    background-color: #2196F3;
    color: #ffffff;
}

.select-recommended-button:hover {
    background-color: #1e88e5;
}

.cancel-button {
    background-color: #ffffff;
    color: #374151;
    border: 1px solid #d1d5db;
}

.cancel-button:hover {
    background-color: #f9fafb;
}

.confirm-button {
    background-color: #2563eb;
    color: #ffffff;
}

.confirm-button:hover {
    background-color: #1d4ed8;
}

.confirm-button.disabled {
    background-color: #9ca3af;
    color: #e5e7eb;
    cursor: not-allowed;
    transform: none;
}

/* Course List Styles */
.course-list {
    margin-top: 16px;
}

.category-section {
    margin-bottom: 24px;
}

.category-title {
    font-weight: 600;
    color: #1d4ed8;
    margin-top: 16px;
    margin-bottom: 8px;
    font-size: 16px;
    border-bottom: 1px solid #93c5fd;
    padding-bottom: 4px;
}

.courses-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 8px;
}

.course-item {
    display: flex;
    align-items: center;
    padding: 8px;
    border-radius: 4px;
    position: relative;
    transition: background-color 0.2s ease;
}

.course-item:hover {
    background-color: #eff6ff;
}

/* Course Checkbox and Label */
.course-checkbox {
    height: 16px;
    width: 16px;
    accent-color: #2563eb;
    margin: 0;
    cursor: pointer;
}

.course-label {
    font-size: 14px;
    color: #374151;
    margin-left: 8px;
    cursor: pointer;
}

.recommended-tag {
    color: #2563eb;
    font-weight: 500;
    margin-left: 4px;
    transition: opacity 0.2s ease;
}

/* Tooltip Styles */
/* Tooltip Styles */
.tooltip {
    visibility: hidden;
    position: absolute;
    z-index: 999;
    bottom: calc(100% + 5px);
    left: 50%;
    transform: translateX(-50%);
    background-color: #5c72f1de;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 8px 12px;
    opacity: 0;
    transition: opacity 0.3s, visibility 0.3s;
    font-size: 12px;
    line-height: 1.4;
    width: max-content;
    max-width: 250px;
    white-space: normal;
    word-wrap: break-word;
    pointer-events: none;
}
.tooltip::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #5c72f1de transparent transparent transparent;
}
/* Show tooltip on hover */
.tooltip-container:hover .tooltip {
    visibility: visible;
    opacity: 1;
}

.course-item:hover .tooltip {
    visibility: visible;
    opacity: 1;
}

/* Sparkling Icon */
.sparkling-icon {
    width: 20px;
    height: 20px;
    vertical-align: middle;
    margin-left: 4px;
}

/* Responsive Styles */
@media (max-width: 640px) {
    .recommendations-container {
        flex-direction: column;
        align-items: flex-start;
    }

    .recommendations-divider {
        display: none;
    }

    .overlay-content {
        margin: 16px;
        max-height: calc(100vh - 32px);
    }

    .courses-container {
        grid-template-columns: 1fr;
    }

    .action-button-container {
        flex-direction: column;
    }

    .action-button {
        width: 100%;
    }
}

/* Animation Classes */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.fade-in {
    animation: fadeIn 0.2s ease-in-out;
}

/* Focus States */
.toggle-button:focus,
.action-button:focus,
.course-checkbox:focus {
    outline: 2px solid #2563eb;
    outline-offset: 2px;
}

.toggle-switch input:focus-visible + .toggle-slider {
    outline: 2px solid #2563eb;
    outline-offset: 2px;
}
  </style>
</head>
<body>
  <button class="btn btn-back" id="backToDetail">← Back</button>
  <div class="glass-container">
    <!-- Header -->
    <h1>Enroll Learners</h1>
    <p>Enroll your learners into the appropriate learning pathways. Each course costs £30 per learner. Select learners individually or use auto-enroll to automatically assign them their AI-recommended courses.</p>

    <!-- Search Bar -->
    <input type="text" id="searchInput" placeholder="Search learners..." oninput="filterLearners()">

    <!-- Pathway Sections Container -->
    <div id="pathwaysContainer">
    </div>


    <!-- Total Cost Section -->
    <div class="total-cost-container mt-8 p-4 bg-gray-100 rounded-lg">
      <div class="order-summary mb-4" id="orderSummary">
        <!-- Order summary content will be dynamically inserted here -->
      </div>
    
      <h2 class="text-2xl font-semibold mb-2">Total Cost</h2>
      <p class="text-3xl font-bold text-orange-500 mb-4">£<span id="totalCost">0</span></p>
    
      <button id="proceedToPayment" class="w-full bg-blue-600 text-white font-bold py-2 px-4 rounded hover:bg-blue-700 transition duration-300">
        <span class="button-text">Confirm Order</span>
        <span class="processing-text hidden">
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Processing...
        </span>
      </button>
    </div>
  </div>
</div>

  <!-- External JavaScript File -->
  <script src="enrollment.js"></script>
</body>
</html>
